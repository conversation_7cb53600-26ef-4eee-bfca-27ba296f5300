<?php
/**
 * Template name: Recordings
 */
get_header(); ?>
<link href="<?php bloginfo( 'template_url' ); ?>/css/imagehover.css" rel="stylesheet" media="all">
<section class="section-slide"> <img src="<?php bloginfo( 'template_url' ); ?>/images/training-banner-1.jpg" alt=""></section>
<section class="section-about section">
  <div class="container">
    <h3 class="newh3">ABOUT THE EVENT</h3>
    <div class="row">
      <div class="col-md-8">
        <?php the_field('main_content')?>
        <div class="button-subs marg-top-40"><a href="https://docs.google.com/forms/d/1qd1InMJBlKFPxU3bjz9_T_jluDnsk6qjdcPvT9eu0Cc/viewform?edit_requested=true&fbclid=PAQ0xDSwKky6JleHRuA2FlbQIxMQABp6E9W-jx_HmQ3VL5mrqscEfZXTYIF2VOeG6qYA9RQdRurhnctKBbRwR7bFQh_aem_0RCYbcGlEV-f01fUNnqDDQ" target="_blank" class="btn-more">Register Here for the Event</a></div>
      </div>
      <div class="col-md-4">
        <div class="recording-div">
          <h3>EVENT DETAILS :</h3>
          <div class="color-div">
            <ul>
              <li><strong>Start:</strong>
                <?php the_field('start_date')?>
              </li>
              <li><strong>End:</strong>
                <?php the_field('end_date')?>
              </li>
              <li><strong>Events Category:</strong>
                <a href="<?php echo home_url( '/' ); ?>teacher-trainings/teacher-training/"><?php the_field('category')?></a>
              </li>
              <li><strong>Events Tags:</strong>
                <a href="<?php echo home_url( '/' ); ?>teacher-trainings/teacher-training/"><?php the_field('tag')?></a>
              </li>
              <li><strong>Price:</strong>
                <?php the_field('price1')?>
              </li>
              <li><strong>Website:</strong> <a href="<?php the_field('website')?>" target="_blank" class="nlink"> Click Here </a> </li>
            </ul>
          </div>
        </div>
        <div class="recording-div organizerul">
          <h3>ORGANIZER :</h3>
          <div class="color-div">
            <ul>
              <li>
                <?php the_field('author_name')?>
              </li>
              <li><img src="<?php if( get_field('author_image') ): ?><?php the_field('author_image'); ?><?php endif; ?>" alt="" /></li>
              <li><?php echo ot_get_option('mdetails'); ?></li>
              <li><strong>Phone:</strong> <?php echo ot_get_option('mphone'); ?> </li>
              <li><strong>Email:</strong> <a href="mailto:<?php echo ot_get_option('memail'); ?>"><?php echo ot_get_option('memail'); ?></a> </li>
              <li><strong>Website:</strong> <a href="<?php echo ot_get_option('mwebsite'); ?>" target="_blank">Click Here</a></li>
              <li><strong>Other Events:</strong> <a href="<?php echo home_url( '/' ); ?>teacher-trainings/teacher-training/">Click Here</a></li>
            </ul>
          </div>
        </div>
        <div class="recording-div">
          <h3>Venue :</h3>
          <div class="color-div">
            <ul>
              <li>
                <?php the_field('program_time')?>
                -
                <?php the_field('youtube_url')?>
              </li>
              <li><a href="<?php the_field('google_map')?>" target="_blank">Google Map</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<section class="section-about section padd-top-0">
  <div class="container">
    <h3 class="newh3">RELATED UPCOMING EVENTS</h3>
    <div id="recording-carousel" class="owl-carousel">
      <?php
		global $post,$wpdb;
		$args = array(
		'post_type' => 'upevents'
		);
		$query = new WP_Query( $args );
		if($query->have_posts() ) {
		while ( $query->have_posts() ) : $query->the_post();
		$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
		$getspostid = $post->ID;
	?>
      <div class="item">
        <div class="demo">
          <figure class="imghvr-push-up"><img src="<?php echo $img_url1; ?>" alt="">
            <h4><?php echo the_title(); ?></h4>
            <figcaption> <?php echo wp_trim_words( get_the_content(), 30, '...' ); ?>
              <p><a href="<?php the_permalink(); ?>" class="buybtn">Read More</a></p>
            </figcaption>
          </figure>
        </div>
      </div>
      <?php endwhile;}
	   wp_reset_postdata();
	?>
    </div>
  </div>
</section>
<?php // get_sidebar(); ?>
<?php get_footer(); ?>