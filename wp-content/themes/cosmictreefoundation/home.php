<?php
/**
 * Template name: Home
 */
get_header(); ?>
<section class="section-slide">
  <div id="home-slide" class="owl-carousel owl-theme">
    <?php
		global $post,$wpdb;
		$args = array(
		'post_type' => 'hslider_post'
		);
		$query = new WP_Query( $args );
		if($query->have_posts() ) {
		while ( $query->have_posts() ) : $query->the_post();
		$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
		$getspostid = $post->ID;
	?>
    <div class="item"> <img src="<?php echo $img_url1; ?>" alt="">
      <div class="slide-content">
        <div class="container">
          <div class="slide-conblock"> <?php echo the_content(); ?> </div>
        </div>
      </div>
    </div>
    <?php endwhile;}
	   wp_reset_postdata();
	?>
  </div>
</section>
<section class="section-about section mob-gap-off">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-5"><?php echo the_post_thumbnail('full'); ?></div>
      <div class="col-md-7 content-column"><?php echo the_content(); ?></div>
    </div>
  </div>
</section>
<section class="section-image-banner section"><a href="<?php echo home_url( '/' ); ?>meet-diana-cooper-in-india/"><img src="<?php the_field('banner_1')?>" alt="" /></a></section>
<section class="section-ev section">
  <div class="container">
    <div class="section-heading">
      <?php the_field('course_text')?>
    </div>
    <div class="row" style="margin-top:50px;">
      <div class="col-md-7 video-block">
        <h3 style="display:none;">
          <?php the_field('class_title')?>
        </h3>
        <div class="video-column">
          <div id="training-carousel" class="owl-carousel owl-theme">
            <?php
				global $post,$wpdb;
				$args = array(
				'post_type' => 'htrainings_post'
				);
				$query = new WP_Query( $args );
				if($query->have_posts() ) {
				while ( $query->have_posts() ) : $query->the_post();
				$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
				$getspostid = $post->ID;
			 ?>
            <div class="item">
              <div class="block-tarot"><a href="<?php echo home_url( '/' ); ?>teacher-trainings/teacher-training/"><img src="<?php echo $img_url1; ?>" alt=""></a></div>
            </div>
            <?php endwhile;}
			   wp_reset_postdata();
			?>
          </div>
        </div>
      </div>
      <div class="col-md-5 event-block">
        <h6>
          <?php the_field('event_title')?>
        </h6>
        <ul class="epos-column row align-items-center">
          <?php
		global $post,$wpdb;
		$args = array(
		'post_type' => 'upevents',
		'posts_per_page' => 6
		);
		$query = new WP_Query( $args );
		if($query->have_posts() ) {
		while ( $query->have_posts() ) : $query->the_post();
		$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
		$getspostid = $post->ID;
	    ?>
          <li>
            <div class="epos-block">
              <div class="thumb"><a href="<?php the_permalink(); ?>"><img src="<?php echo $img_url1; ?>" alt=""></a></div>
              <div class="content">
                <h4><a href="<?php the_permalink(); ?>"><?php echo the_title(); ?></a></h4>
                <p><?php echo wp_trim_words( get_the_content(), 10, '...' ); ?></p>
                <p>
                  <?php the_field('program_date')?>
                  <a href="<?php the_permalink(); ?>">Read More....</a></p>
              </div>
            </div>
          </li>
          <?php endwhile;}
	       wp_reset_postdata();
	     ?>
        </ul>
        <h5><a href="<?php echo home_url( '/' ); ?>teacher-trainings/teacher-training/">View More Trainings</a></h5>
      </div>
    </div>
  </div>
</section>
<section class="section-per">
  <div class="container">
    <ul id="counter">
      <li><strong class="counter-value" data-count="5000"></strong><strong>+</strong><span>Students</span></li>
      <li><strong class="counter-value" data-count="28"></strong><strong>+</strong><span>Countries</span></li>
      <li><strong class="counter-value" data-count="100"></strong><strong>%</strong><span>Satisfaction</span></li>
    </ul>
  </div>
</section>
<div class="container">
  <section class="section-serv section">
    <div class="row">
      <div class="col-md-5">
        <h3>
          <?php the_field('service_title')?>
        </h3>
        <div class="faq-accordion" id="accordionExample">
          <div class="accordion-item">
            <h4 class="accordion-header" id="headingOne"><a class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
              <?php the_field('title_1')?>
              </a></h4>
            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
              <div class="accordion-body">
                <?php the_field('content_1')?>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h4 class="accordion-header" id="headingTwo"><a class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
              <?php the_field('title_2')?>
              </a></h4>
            <div id="collapseTwo" class="accordion-collapse collapse show" aria-labelledby="headingTwo" data-bs-parent="#accordionExample" style="">
              <div class="accordion-body">
                <?php the_field('content_2')?>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h4 class="accordion-header" id="headingThree"><a class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
              <?php the_field('title_3')?>
              </a></h4>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample" style="">
              <div class="accordion-body">
                <?php the_field('content_3')?>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h4 class="accordion-header" id="heading4"><a class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapseThree">
              <?php the_field('title_4')?>
              </a></h4>
            <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4" data-bs-parent="#accordionExample">
              <div class="accordion-body">
                <?php the_field('content_4')?>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h4 class="accordion-header" id="heading5"><a class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
              <?php the_field('title_5')?>
              </a></h4>
            <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5" data-bs-parent="#accordionExample">
              <div class="accordion-body">
                <?php the_field('content_5')?>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h4 class="accordion-header" id="heading6"><a class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse6" aria-expanded="false" aria-controls="collapse6">
              <?php the_field('title_6')?>
              </a></h4>
            <div id="collapse6" class="accordion-collapse collapse" aria-labelledby="heading6" data-bs-parent="#accordionExample">
              <div class="accordion-body">
                <?php the_field('content_6')?>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-7 mob-marg">
        <h3 class="mob-view" style="margin:20px;">Workshops</h3>
        <a href="<?php echo home_url( '/' ); ?>workshops/"><img src="<?php the_field('service_image')?>" alt=""></a></div>
    </div>
  </section>
  <section class="section-tarot section">
    <div class="container">
      <div class="section-heading">
        <?php the_field('tarot_text_1')?>
      </div>
      <div id="tarot-carousel" class="owl-carousel owl-theme">
        <?php
		global $post,$wpdb;
		$args = array(
		'post_type' => 'htread_post'
		);
		$query = new WP_Query( $args );
		if($query->have_posts() ) {
		while ( $query->have_posts() ) : $query->the_post();
		$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
		$getspostid = $post->ID;
	  ?>
        <div class="item">
          <div class="block-tarot"> <img src="<?php echo $img_url1; ?>" alt=""> <?php echo the_content(); ?> </div>
        </div>
        <?php endwhile;}
	   wp_reset_postdata();
	  ?>
      </div>
    </div>
  </section>
  <div class="button-subs marg-top-30"><a href="#subscribe" class="btn-more">Subscribe to our Newsletter</a></div>
</div>
<section class="section-client section">
  <div class="container">
    <?php the_field('testimonial_text')?>
    <div id="textimonial-carousel" class="owl-carousel">
      <?php
		global $post,$wpdb;
		$args = array(
		'post_type' => 'hestimonials_post'
		);
		$query = new WP_Query( $args );
		if($query->have_posts() ) {
		while ( $query->have_posts() ) : $query->the_post();
		$img_url1 = wp_get_attachment_url(get_post_thumbnail_id($post->ID));
		$getspostid = $post->ID;
	  ?>
      <div class="item">
        <div class="client-block">
          <div class="thum-image"><a href="<?php echo home_url( '/' ); ?>video-testimonials/"><img src="<?php echo $img_url1; ?>" alt=""></a></div>
        </div>
      </div>
      <?php endwhile;}
	   wp_reset_postdata();
	  ?>
    </div>
    <div class="button-subs"><a href="<?php echo home_url( '/' ); ?>video-testimonials/" class="btn-more">Video Testimonials</a></div>
  </div>
</section>
<section class="section-contact section">
  <div class="container">
    <div class="row">
      <div class="col-md-8">
        <div class="contact-form">
          <div class="form-block"> <?php echo do_shortcode("[contact-form-7 id=61 title=Home Contact Form]"); ?> </div>
        </div>
      </div>
      <div class="col-md-4">
        <h3>Reach Us</h3>
        <div class="info">
          <ul>
            <li><?php echo ot_get_option('address'); ?></li>
            <li><?php echo ot_get_option('phone'); ?></li>
            <li><?php echo ot_get_option('email'); ?></li>
          </ul>
        </div>
        <figure class="contact-img"><img src="<?php the_field('contact_image')?>" alt=""></figure>
      </div>
    </div>
  </div>
</section>
<?php // get_sidebar(); ?>
<?php get_footer(); ?>