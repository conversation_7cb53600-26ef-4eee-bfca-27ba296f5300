<?php
/**
 * Template name: In-Person Workshop
 */
get_header(); ?>
<section class="section-slide"><?php echo the_post_thumbnail('full'); ?></section>
<section class="section-about section">
  <div class="container">
    <h3 class="newh3">IN-PERSON WORKSHOPS</h3>
     <?php $the_query = new WP_Query( array('posts_per_page'=>15,
                                 'post_type'=>'hworkshop',
								 'cat'=>'20',
                                 'paged' => get_query_var('paged') ? get_query_var('paged') : 1) 
                            ); 
                            ?>
    <?php while ($the_query -> have_posts()) : $the_query -> the_post(); ?>    
    <div class="color-div">
        <ul>
          <li><i class="fa fa-calendar" aria-hidden="true"></i> <?php the_field('program_date')?></li>
          <li><i class="fa fa-globe" aria-hidden="true"></i> <?php the_field('program_time')?> - <?php the_field('youtube_url')?></li>
          <li><?php the_field('price1')?></li>
        </ul>
      </div>
    <div class="events-div">
    	<div class="row">
        	<div class="col-md-4"><?php echo the_post_thumbnail('full'); ?></div>
            <div class="col-md-4"><div class="events-div1">
				<h3 class="mob-view"><?php echo the_title(); ?></h3>
                <p><?php echo wp_trim_words( get_the_content(), 55, '...' ); ?></p>
                <div><a href="<?php the_permalink(); ?>" class="buybtn2">Find Out More</a></div>
                </div>
            </div>
            <div class="col-md-4">
            	<div class="mob-no-view"><img src="<?php if( get_field('author_image') ): ?><?php the_field('author_image'); ?><?php endif; ?>" alt="" />
                <div class="author-title"><?php if( get_field('author_name') ): ?><?php the_field('author_name'); ?><?php endif; ?></strong></div></div>
            </div>
        </div>
    </div>
    <?php
endwhile;
$big = 999999999; // need an unlikely integer
echo '<div class="pagination">';
 echo paginate_links( array(
    'base' => str_replace( $big, '%#%', get_pagenum_link( $big ) ),
    'format' => '?paged=%#%',
    'current' => max( 1, get_query_var('paged') ),
    'total' => $the_query->max_num_pages
) );
echo '</div>',
wp_reset_postdata(); ?>
  </div>
</section>
<?php // get_sidebar(); ?>
<?php get_footer(); ?>