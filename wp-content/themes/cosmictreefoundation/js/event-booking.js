/**
 * Event Booking JavaScript
 * Handles form validation, pass selection, coupon code, and payment processing
 */
jQuery(document).ready(function ($) {
  console.log("Event Booking JS Loaded:", eventBooking);

  // Variables to track state
  let selectedPassType = null;
  let selectedPassPrice = 0;
  let appliedCoupon = null;
  let couponDiscount = 0;
  let totalAmount = 0;

  // Form field validation functions
  const validators = {
    pass_type: function (value) {
      if (!value || value === "") {
        return "Please select a pass type.";
      }
      return "";
    },

    ticket_qty: function (value) {
      const qty = parseInt(value);
      const max = parseInt($("#ticket_qty").attr("max") || 1); // Set default max to 1

      if (!qty || isNaN(qty)) {
        return "Please select a ticket quantity.";
      }
      if (qty <= 0) {
        return "Ticket quantity must be greater than zero.";
      }
      if (qty > max) {
        return "Maximum 1 ticket allowed per order."; // Hardcoded message for max 1 ticket
      }
      return "";
    },

    customer_name: function (value) {
      if (!value || value.trim() === "") {
        return "Please enter your name.";
      }
      if (value.trim().length < 3) {
        return "Name must be at least 3 characters long.";
      }
      return "";
    },

    customer_email: function (value) {
      if (!value || value.trim() === "") {
        return "Please enter your email address.";
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return "Please enter a valid email address.";
      }
      return "";
    },

    customer_phone: function (value) {
      if (!value || value.trim() === "") {
        return "Please enter your phone number with country code.";
      }
      return "";
    },

    customer_age: function (value) {
      if (!value || value.trim() === "") {
        return "Please enter your age.";
      }
      const age = parseInt(value);
      if (isNaN(age) || age < 1 || age > 120) {
        return "Please enter a valid age between 1 and 120.";
      }
      return "";
    },

    customer_city: function (value) {
      if (!value || value.trim() === "") {
        return "Please enter your city.";
      }
      if (value.trim().length < 2) {
        return "City name must be at least 2 characters long.";
      }
      return "";
    },
  };

  // Initialize the order summary
  updateOrderSummary();

  // Handle pass selection
  $(".pass-option").click(function () {
    // Skip if the pass type is sold out (radio button is disabled)
    if ($(this).find("input[type='radio']").prop("disabled")) {
      return;
    }

    // Remove selected class from all options
    $(".pass-option").removeClass("border-blue-500 bg-blue-50");

    // Add selected class to clicked option
    $(this).addClass("border-blue-500 bg-blue-50");

    // Check the radio button
    $(this).find("input[type='radio']").prop("checked", true);

    // Update selected pass type and price
    selectedPassType = $(this).data("pass-type");
    selectedPassPrice = parseFloat($(this).data("price"));

    // Clear any pass type error message when a selection is made
    $(".pass-options-container")
      .closest(".mb-4")
      .find(".field-error")
      .addClass("hidden");

    // Update order summary
    updateOrderSummary();
  });

  // Handle ticket quantity changes
  $("#ticket_qty").on("input change", function () {
    // Validate ticket quantity immediately
    const qty = parseInt($(this).val());
    const max = parseInt($(this).attr("max") || 1);
    const errorElement = $(this).closest(".mb-4").find(".field-error");

    // Clear previous error
    errorElement.addClass("hidden");
    $(this).removeClass("border-red-500");

    // Check if quantity exceeds maximum
    if (qty > max) {
      errorElement
        .text("Maximum 1 ticket allowed per order.")
        .removeClass("hidden");
      $(this).addClass("border-red-500");
    }

    updateOrderSummary();
  });

  // Handle coupon code application
  $("#apply-coupon").click(function () {
    const couponCode = $("#coupon_code").val().trim().toUpperCase();
    const couponMessage = $("#coupon-message");

    if (!couponCode) {
      couponMessage
        .text("Please enter a coupon code.")
        .removeClass("hidden text-green-600")
        .addClass("text-red-600");
      return;
    }

    // Check if the coupon code is valid
    if (couponCode === "EL1000" || couponCode === "MGH100DC") {
      // Store the coupon code
      appliedCoupon = couponCode;

      // Disable the coupon input and button
      $("#coupon_code").prop("disabled", true);
      $(this)
        .text("Applied")
        .prop("disabled", true)
        .removeClass("bg-blue-600 hover:bg-blue-700")
        .addClass("bg-green-600");

      // Initial message (will be updated in updateOrderSummary with actual amount)
      let initialMessage = "";
      if (couponCode === "EL1000") {
        initialMessage = "Coupon applied! ₹1,000 discount will be applied.";
      } else if (couponCode === "MGH100DC") {
        initialMessage = "Coupon applied! 100% discount will be applied.";
      }

      couponMessage
        .text(initialMessage)
        .removeClass("hidden text-red-600")
        .addClass("text-green-600");

      // Update order summary to calculate and display the actual discount
      updateOrderSummary();
    } else {
      // Invalid coupon code
      appliedCoupon = null;
      couponDiscount = 0;
      couponMessage
        .text("Invalid coupon code.")
        .removeClass("hidden text-green-600")
        .addClass("text-red-600");
    }
  });

  // Real-time validation on input fields
  $(
    "#ticket_qty, #customer_name, #customer_email, #customer_phone, #customer_age, #customer_city"
  ).on("input blur", function () {
    const field = $(this).attr("id");
    const value = $(this).val().trim();
    const error = validators[field](value);

    // For ticket_qty, find the error element in the flex container
    const errorElement =
      field === "ticket_qty"
        ? $(this).closest(".mb-4").find(".field-error")
        : $(this).siblings(".field-error");

    if (error) {
      errorElement.text(error).removeClass("hidden");
      $(this).addClass("border-red-500");
    } else {
      errorElement.addClass("hidden");
      $(this).removeClass("border-red-500");
    }

    // Special handling for ticket quantity
    if (field === "ticket_qty") {
      const qty = parseInt(value);
      if (qty > 1) {
        errorElement
          .text("Maximum 1 ticket allowed per order.")
          .removeClass("hidden");
        $(this).addClass("border-red-500");
      }
      updateOrderSummary();
    }
  });

  // Add validation for pass type selection
  $(".pass-option").on("click", function () {
    // Clear any pass type error message when a selection is made
    $(".pass-options-container")
      .closest(".mb-4")
      .find(".field-error")
      .addClass("hidden");
  });

  // Function to update order summary
  function updateOrderSummary() {
    const quantity = parseInt($("#ticket_qty").val()) || 1;
    let passTypeText = "-";
    let unitPrice = 0;

    if (selectedPassType === "gold") {
      passTypeText = "Gold Pass";
      unitPrice = selectedPassPrice;
    } else if (selectedPassType === "violet") {
      passTypeText = "Violet Pass";
      unitPrice = selectedPassPrice;
    }

    // Calculate total before discount
    let subtotal = unitPrice * quantity;

    // Apply discount if coupon is applied
    let discount = 0;

    if (appliedCoupon) {
      if (appliedCoupon === "MGH100DC") {
        // 100% discount for MGH100DC
        discount = subtotal; // Full amount discount
        couponDiscount = discount; // Update the global couponDiscount variable

        // Update coupon message to show the actual discount amount
        $("#coupon-message")
          .text(
            "Coupon applied successfully! ₹" +
              discount.toFixed(2) +
              " discount (100%) applied."
          )
          .removeClass("hidden text-red-600")
          .addClass("text-green-600");
      } else if (appliedCoupon === "EL1000") {
        // Fixed discount of ₹1000 for EL1000
        discount = 1000;

        // Ensure discount doesn't exceed subtotal
        if (discount > subtotal) {
          discount = subtotal;
        }

        couponDiscount = discount; // Update the global couponDiscount variable

        // Update coupon message to show the actual discount amount
        $("#coupon-message")
          .text(
            "Coupon applied successfully! ₹" +
              discount.toFixed(2) +
              " discount applied."
          )
          .removeClass("hidden text-red-600")
          .addClass("text-green-600");
      }
    }

    // Calculate final total
    totalAmount = subtotal - discount;

    // Update summary elements
    $("#summary-pass-type").text(passTypeText);
    $("#summary-unit-price").text("₹" + unitPrice.toFixed(2));
    $("#summary-quantity").text(quantity);

    if (discount > 0) {
      $("#summary-discount").text("-₹" + discount.toFixed(2));
      $("#discount-row").show();
    } else {
      $("#discount-row").hide();
    }

    $("#summary-total").text("₹" + totalAmount.toFixed(2));
  }

  // Handle payment button click
  $("#rzp-button").click(function () {
    // Disable button to prevent multiple clicks
    $(this).prop("disabled", true).text("Processing...");

    // Get form values
    var ticket_qty = parseInt($("#ticket_qty").val());
    var customer_name = $("#customer_name").val().trim();
    var customer_email = $("#customer_email").val().trim();
    var customer_phone = $("#customer_phone").val().trim();
    var customer_age = $("#customer_age").val().trim();
    var customer_city = $("#customer_city").val().trim();
    var error_message = $("#error-message");
    var hasErrors = false;

    // Clear previous error messages
    error_message.addClass("hidden").text("");
    $(".field-error").addClass("hidden");

    // Check if a pass type is selected
    if (!selectedPassType) {
      // Show inline error for pass type instead of at the top
      const passTypeError = $(".pass-options-container")
        .closest(".mb-4")
        .find(".field-error");
      passTypeError.text("Please select a pass type.").removeClass("hidden");
      $(this).prop("disabled", false).text("Pay Now");

      // Delay scrolling slightly to ensure DOM is updated
      setTimeout(function () {
        // Scroll to the pass type error
        $("html, body").animate(
          {
            scrollTop: passTypeError.offset().top - 50,
          },
          300
        );
      }, 100);

      return;
    }

    // Validate all fields
    const fields = [
      "ticket_qty",
      "customer_name",
      "customer_email",
      "customer_phone",
      "customer_age",
      "customer_city",
    ];
    const values = {
      ticket_qty: ticket_qty,
      customer_name: customer_name,
      customer_email: customer_email,
      customer_phone: customer_phone,
      customer_age: customer_age,
      customer_city: customer_city,
    };

    let errorMessages = [];

    fields.forEach(function (field) {
      const error = validators[field](values[field]);
      if (error) {
        // For ticket_qty, find the error element in the flex container
        const errorElement =
          field === "ticket_qty"
            ? $("#" + field)
                .closest(".mb-4")
                .find(".field-error")
            : $("#" + field).siblings(".field-error");

        errorElement.text(error).removeClass("hidden");
        $("#" + field).addClass("border-red-500");
        errorMessages.push(error);
        hasErrors = true;
      }
    });

    // If there are validation errors, focus on the first error field
    if (hasErrors) {
      $(this).prop("disabled", false).text("Pay Now");

      // Find the first field with an error
      const firstErrorField = fields.find((field) =>
        validators[field](values[field])
      );

      if (firstErrorField) {
        // Delay scrolling slightly to ensure DOM is updated
        setTimeout(function () {
          // Get the appropriate error element
          const errorElement =
            firstErrorField === "ticket_qty"
              ? $("#" + firstErrorField)
                  .closest(".mb-4")
                  .find(".field-error")
              : $("#" + firstErrorField).siblings(".field-error");

          // Scroll directly to the first field with error
          $("html, body").animate(
            {
              scrollTop: errorElement.offset().top - 50,
            },
            300,
            function () {
              // Focus on the first field with error
              $("#" + firstErrorField).focus();
            }
          );
        }, 100);
      }

      return;
    }

    $.ajax({
      url: eventBooking.ajax_url,
      type: "POST",
      data: {
        action: "create_order",
        nonce: eventBooking.nonce,
        event_id: eventBooking.event_id,
        ticket_qty: ticket_qty,
        customer_name: customer_name,
        customer_email: customer_email,
        customer_phone: customer_phone,
        customer_age: customer_age,
        customer_city: customer_city,
        pass_type: selectedPassType,
        pass_price: selectedPassPrice,
        coupon_code: appliedCoupon,
        coupon_discount: couponDiscount,
        total_amount: totalAmount,
      },
      success: function (response) {
        if (response.success) {
          // Check if this is a free order (100% discount with MGH100DC coupon)
          if (response.data.is_free_order) {
            // Show redirect loader
            showRedirectLoader();

            // For free orders, skip payment gateway and process directly
            $.ajax({
              url: eventBooking.ajax_url,
              type: "POST",
              data: {
                action: "process_free_order",
                nonce: eventBooking.nonce,
                order_id: response.data.order_id,
              },
              success: function (free_order_response) {
                if (free_order_response.success) {
                  window.location.href = free_order_response.data.redirect;
                } else {
                  hideRedirectLoader();
                  error_message
                    .text(
                      free_order_response.data.message ||
                        "Failed to process free order. Please try again."
                    )
                    .removeClass("hidden");
                  $("#rzp-button").prop("disabled", false).text("Pay Now");
                }
              },
              error: function (xhr, status, error) {
                hideRedirectLoader();
                console.error("Free order processing error:", status, error);

                // Display error message
                error_message
                  .text("Failed to process free order. Please try again.")
                  .removeClass("hidden");

                // Delay scrolling slightly to ensure DOM is updated
                setTimeout(function () {
                  // Scroll to error message
                  $("html, body").animate(
                    {
                      scrollTop: error_message.offset().top - 50,
                    },
                    300
                  );
                }, 100);

                $("#rzp-button").prop("disabled", false).text("Pay Now");
              },
            });
          } else {
            // Regular paid order - use Razorpay
            var options = {
              key: eventBooking.razorpay_key,
              amount: response.data.amount,
              currency: response.data.currency,
              name: "Event Booking",
              description: "Payment for Event #" + eventBooking.event_id,
              order_id: response.data.razorpay_order_id,
              handler: function (payment_response) {
                // Show redirect loader
                showRedirectLoader();

                $.ajax({
                  url: eventBooking.ajax_url,
                  type: "POST",
                  data: {
                    action: "verify_payment",
                    nonce: eventBooking.nonce,
                    order_id: response.data.order_id,
                    razorpay_payment_id: payment_response.razorpay_payment_id,
                    razorpay_order_id: payment_response.razorpay_order_id,
                    razorpay_signature: payment_response.razorpay_signature,
                  },
                  success: function (verify_response) {
                    if (verify_response.success) {
                      window.location.href = verify_response.data.redirect;
                    } else {
                      window.location.href = verify_response.data.redirect;
                    }
                  },
                  error: function (xhr, status, error) {
                    hideRedirectLoader();
                    console.error(
                      "Payment verification AJAX error:",
                      status,
                      error
                    );

                    // Display error message
                    error_message
                      .text("Payment verification failed. Please try again.")
                      .removeClass("hidden");

                    // Delay scrolling slightly to ensure DOM is updated
                    setTimeout(function () {
                      // Scroll to error message
                      $("html, body").animate(
                        {
                          scrollTop: error_message.offset().top - 50,
                        },
                        300
                      );
                    }, 100);

                    $("#rzp-button").prop("disabled", false).text("Pay Now");
                  },
                });
              },
              prefill: {
                name: response.data.customer_name,
                email: response.data.customer_email,
                contact: response.data.customer_phone,
              },
              theme: {
                color: "#38A169",
              },
              modal: {
                ondismiss: function () {
                  $("#rzp-button").prop("disabled", false).text("Pay Now");
                },
              },
            };
            var rzp = new Razorpay(options);
            rzp.on("payment.failed", function (response) {
              console.error("Payment failed:", response.error);

              // Display error message
              error_message
                .text("Payment failed: " + response.error.description)
                .removeClass("hidden");

              // Scroll to error message
              $("html, body").animate(
                {
                  scrollTop: error_message.offset().top - 100,
                },
                500
              );

              $("#rzp-button").prop("disabled", false).text("Pay Now");
            });
            rzp.open();
          }
        } else {
          console.error("Order creation failed:", response.data);
          // Display error message
          error_message.text(response.data).removeClass("hidden");

          // Delay scrolling slightly to ensure DOM is updated
          setTimeout(function () {
            // Scroll to error message
            $("html, body").animate(
              {
                scrollTop: error_message.offset().top - 50,
              },
              300
            );
          }, 100);

          $("#rzp-button").prop("disabled", false).text("Pay Now");
        }
      },
      error: function (xhr, status, error) {
        console.error("Order creation AJAX error:", status, error);

        // Display error message
        error_message
          .text("Failed to create order. Please try again.")
          .removeClass("hidden");

        // Delay scrolling slightly to ensure DOM is updated
        setTimeout(function () {
          // Scroll to error message
          $("html, body").animate(
            {
              scrollTop: error_message.offset().top - 50,
            },
            300
          );
        }, 100);

        $("#rzp-button").prop("disabled", false).text("Pay Now");
      },
    });
  });

  // Function to show redirect loader
  function showRedirectLoader() {
    // Create overlay if it doesn't exist
    if ($("#redirect-loader").length === 0) {
      var overlay = $(
        '<div id="redirect-loader" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 9999; display: flex; justify-content: center; align-items: center; flex-direction: column;"></div>'
      );
      var spinner = $(
        '<div style="border: 5px solid #f3f3f3; border-top: 5px solid #f7941d; border-radius: 50%; width: 50px; height: 50px; animation: spin 2s linear infinite;"></div>'
      );
      var message = $(
        '<p style="color: white; margin-top: 20px; font-size: 18px;">Payment successful! Redirecting to confirmation page...</p>'
      );

      // Add CSS animation
      $("head").append(
        "<style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>"
      );

      overlay.append(spinner).append(message);
      $("body").append(overlay);
    } else {
      $("#redirect-loader").show();
    }
  }

  // Function to hide redirect loader
  function hideRedirectLoader() {
    $("#redirect-loader").hide();
  }
});
